import React from "react";

const Retailer = () => {
  return (
    <nav className="bg-green-900 text-white px-8 py-8 flex items-center justify-between">
      {/* Logo */}
      <div className="flex items-center gap-1">
        <img
          src="./casio_1.jpg"
          alt="Rolex Logo"
          className="h-10"
        />
        <span className="   text-3xl">OFFICIAL RETAILER</span>
      </div>

      {/* Menu */}
      <ul className="flex space-x-1  mb-10 ">
        <li className="hover:text-yellow-400 cursor-pointer">Discover Rolex</li>
        <li className="hover:text-yellow-400 cursor-pointer">Rolex Watches</li>
        <li className="hover:text-yellow-400 cursor-pointer">New Watches 2025</li>
        <li className="hover:text-yellow-400 cursor-pointer">Watchmaking</li>
        <li className="hover:text-yellow-400 cursor-pointer">Servicing</li>
        <li className="hover:text-yellow-400 cursor-pointer">World of Rolex</li>
        <li className="hover:text-yellow-400 cursor-pointer">Rolex at Swiss Time House</li>
        <li className="hover:text-yellow-400 cursor-pointer">Contact Us</li>
      </ul>
    </nav>
  );
};

export default Retailer;
