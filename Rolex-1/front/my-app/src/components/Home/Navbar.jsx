import React from "react";

function Navbar() {
  return (
    <div className="fixed top-0 left-0 w-full bg-red-800 h-12 flex items-center justify-between px-4 z-50">
      {/* Left: Contact */}
      <div className="text-white font-medium text-sm">
        📞 9958815201
      </div>

      {/* Middle: Running Marquee Banner */}
      <div className="absolute left-1/2 transform -translate-x-1/2 w-1/2 overflow-hidden">
        <div
          className="whitespace-nowrap will-change-transform inline-block
                     animate-[marquee_12s_linear_infinite]"
        >
          🚨 Disclaimer: This is a running banner text in the middle 🚨 Disclaimer: This is a running banner text in the middle 🚨
        </div>
      </div>

      {/* Right: Currency Selector */}
      <div className="flex items-center space-x-2 text-white">
        <label
          htmlFor="options"
          className="text-sm font-medium"
        >
          ₹
        </label>
        <select
          name="currency"
          id="options"
          className="bg-transparent text-white border border-white rounded px-2 py-1 text-sm focus:outline-none"
        >
          <option value="INR">INR</option>
          <option value="EUR">EUR</option>
          <option value="USD">USD</option>
        </select>
      </div>
    </div>
  );
}

export default Navbar;
