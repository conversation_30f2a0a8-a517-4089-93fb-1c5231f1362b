import React, { useState } from "react";
import Menu from "../function/Menu";

function Navbar() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div>
      {/* Navbar */}
      <div className="flex items-center justify-between w-full px-9 py-7 border-t-2 border-red-800">
        {/* Menu Button */}
        <button
          onClick={() => setIsOpen(true)}
          className="font-bold  text-white px-3 py-1 rounded"
        >
         <p className="fixed font-bold  bg-white text-black px-3 py-1 rounded"> MENU ☰</p>
        </button>

        {/* Title */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-red-700">SWISS TIME HOUSE</h1>
          <p className="text-sm text-red-600">Since 1946</p>
        </div>

        {/* Logo */}
        <img
          src="/Rolex-image.png"
          alt="Rolex Logo"
          className="h-16 object-contain"
        />
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sliding Manu */}
      <div
        className={`fixed top-0 left-0 h-full w-3/4 sm:w-2/3 lg:w-1/2 bg-white z-50 transform transition-transform duration-300 ${
          isOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <button
          onClick={() => setIsOpen(false)}
          className="absolute top-4 right-4 text-2xl font-bold text-red-600"
        >
          ×
        </button>
        <Menu />
      </div>
    </div>
  );
}

export default Navbar;
