import React from "react";

function StoreLocations() {
  const locations = [
    { name: "Kerala", img: "/kerala.avif" },
    { name: "Tamil Nadu", img: "/tamil nadu.avif" },
    { name: "Karnataka", img: "/karnataka.avif" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", img: "/puducherry.avif" },
    { name: "Telangana", img: "/telengana.avif" },
    { name: "Uttar Pradesh", img: "/UP.avif" },
  ];

  return (
    <div className="bg-white py-16 px-6">
      {/* Heading */}
      <div className="text-center mb-12">
        <p className="text-gray-500 text-sm tracking-widest uppercase">
          Where to Find Us ?
        </p>
        <h2 className="text-4xl font-extrabold text-black mt-2">
          Swiss Time House <br /> Experience Near You
        </h2>
        <div className="mx-auto mt-3 w-20 h-[2px] bg-black"></div>
      </div>

      {/* Locations */}
      <div className="flex justify-center flex-wrap gap-12">
        {locations.map((loc, index) => (
          <div
            key={index}
            className="flex flex-col items-center text-center"
          >
            <div className="h-35 w-35 rounded-full bg-gray-100 flex items-center justify-center shadow-md hover:scale-105 transition">
              <img
                src={loc.img}
                alt={loc.name}
                className="h-28 w-28 object-contain"
              />
            </div>
            <p className="mt-4 font-semibold text-lg">{loc.name}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

export default StoreLocations;
