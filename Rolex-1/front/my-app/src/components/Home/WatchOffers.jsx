import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules"; // ✅ FIXED
import "swiper/css";
import "swiper/css/navigation";

const WatchOffers = () => {
  const row1Watches = [
    { name: "Longines", model: "L37814066", price: "€ 2,020", img: "/watch1.avif" },
    { name: "Longines", model: "L28934926", price: "€ 2,575", img: "/watch2.avif" },
    { name: "Rado", model: "R27086712", price: "€ 3,686", img: "/watch3.avif" },
    { name: "Fossil", model: "ME3098", price: "€ 222", img: "/watch4.avif" },
    { name: "<PERSON>", model: "DW00100672K", price: "€ 214", img: "/watch5.avif" },
    { name: "Citizen", model: "BM7630-80X", price: "€ 283", img: "/watch6.avif" },
  ];

  return (
    <div className="bg-white px-10 py-16">
      {/* Row 1 */}
      <div className="flex items-start gap-6 mb-16">
        {/* Fixed First Card */}
        <div className="w-64 h-[420px] bg-black text-white p-6 rounded-xl flex flex-col justify-between">
          <div>
            <p className="text-sm text-yellow-400">Luxury & Premium Brands</p>
            <h2 className="text-2xl font-bold leading-tight mt-2">
              Take A Look On <br /> Our Premium <br /> Collection
            </h2>
          </div>
          <img
            src="/Demo1.png"
            alt="premium watch"
            className="w-full h-56 object-contain"
          />
          <p className="text-sm">Experience In-Store • India</p>
        </div>

        {/* Slider */}
        <Swiper
          modules={[Autoplay, Navigation]} // ✅ added Navigation in case you need arrows
          slidesPerView={3}
          spaceBetween={30}
          autoplay={{ delay: 4000, disableOnInteraction: false }}
          loop={true}
          className="flex-1"
        >
          {row1Watches.map((watch, index) => (
            <SwiperSlide key={index}>
              <div className="bg-white shadow-md rounded-lg p-4 text-center">
                <img src={watch.img} alt={watch.name} className="h-48 mx-auto" />
                <h3 className="mt-3 font-semibold">{watch.name}</h3>
                <p className="text-gray-500 text-sm">{watch.model}</p>
                <p className="mt-1 font-bold">{watch.price}</p>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default WatchOffers;
