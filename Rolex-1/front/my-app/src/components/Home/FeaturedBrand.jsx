import React from "react";

function FeatureBrand() {
  const brands = [
    { name: "<PERSON><PERSON><PERSON>", image: "/casio_1.jpg" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", image: "/movado.jpg" },
    { name: "<PERSON><PERSON><PERSON>", image: "/rado.avif" },
    { name: "TISSOT", image: "/tissot_1.avif" },
    { name: "SEIKO", image: "/seiko.avif" },
    { name: "LONGINES", image: "/longines.avif" },
    { name: "GUESS", image: "/guess.avif" },
  ];

  return (
    <div className="bg-black min-h-screen w-full py-10">
      {/* Heading */}
      <h2 className="text-white text-center text-3xl font-bold mb-10">
        FEATURED BRANDS
      </h2>

      {/* Grid of brands */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 px-6">
        {brands.map((brand, index) => (
          <div
            key={index}
            className="relative group overflow-hidden rounded-lg shadow-lg"
          >
            {/* Brand Image */}
            <img
              src={brand.image}
              alt={brand.name}
              className="w-full h-64 object-cover transform group-hover:scale-110 transition duration-500"
            />

            {/* Brand Name Overlay */}
            <div className="absolute inset-0 flex items-center justify-center">
              {/* Background overlay */}
              <div className="absolute inset-0 bg-black opacity-40 group-hover:opacity-60 transition duration-500"></div>

              {/* Brand Name (always visible) */}
              <h3 className="relative text-white text-2xl font-bold z-10">
                {brand.name}
              </h3>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default FeatureBrand;
