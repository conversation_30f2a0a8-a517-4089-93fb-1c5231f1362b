import { useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

const images = [
  { id: 1, title: "Watch 1", img: "/watch10.avif" },
  { id: 2, title: "Watch 2", img: "watch8.avif" },
  { id: 3, title: "Watch 3", img: "watch7.png" },
  { id: 4, title: "Watch 4", img: "watch5.avif" },
  { id: 5, title: "Watch 5", img: "watch4.avif" },
  { id: 6, title: "Watch 6", img: "https://via.placeholder.com/300" },
  { id: 7, title: "Watch 7", img: "https://via.placeholder.com/300" },
  { id: 8, title: "Watch 8", img: "https://via.placeholder.com/300" },
  { id: 9, title: "Watch 9", img: "https://via.placeholder.com/300" },
  { id: 10, title: "Watch 10", img: "https://via.placeholder.com/300" },
  { id: 11, title: "Watch 11", img: "https://via.placeholder.com/300" },
  { id: 12, title: "Watch 12", img: "https://via.placeholder.com/300" },
  { id: 13, title: "Watch 13", img: "https://via.placeholder.com/300" },
];

export default function Carousel() {
  const carouselRef = useRef(null);

  const scroll = (direction) => {
    if (carouselRef.current) {
      const scrollAmount =
        direction === "left"
          ? -carouselRef.current.offsetWidth
          : carouselRef.current.offsetWidth;
      carouselRef.current.scrollBy({ left: scrollAmount, behavior: "smooth" });
    }
  };

  return (
    <div className="relative w-full">
        <div className="mt-10 text-3xl font-bold ml-[52px]">
            Whats new 
        </div>
      {/* Left Button */}
      <button
        onClick={() => scroll("left")}
        className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white shadow-md p-2 rounded-full"
      >
        <ChevronLeft />
      </button>

      {/* Carousel */}
      <div
        ref={carouselRef}
        className="flex overflow-x-auto scroll-smooth no-scrollbar px-12"
        style={{ scrollSnapType: "x mandatory" }}
      >
        {images.map((item) => (
          <div
            key={item.id}
            className="min-w-[18%] flex-shrink-0 scroll-snap-align-start bg-white rounded-xl shadow-md overflow-hidden mx-2"
          >
            <img src={item.img} alt={item.title} className="w-full h-40 object-cover" />
            <div className="p-2 font-semibold">{item.title}</div>
          </div>
        ))}
      </div>

      {/* Right Button */}
      <button
        onClick={() => scroll("right")}
        className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white shadow-md p-2 rounded-full"
      >
        <ChevronRight />
      </button>
    </div>
  );
}
