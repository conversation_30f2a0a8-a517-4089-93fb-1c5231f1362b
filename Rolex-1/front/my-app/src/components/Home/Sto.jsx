import React from "react";
import { FaFacebookF, FaInstagram, FaYoutube, FaLinkedinIn } from "react-icons/fa";

function Sto() {
  return (
    <div className="bg-black text-white py-12 px-6 flex flex-col items-center">
      {/* Logo + Brand */}
      <div className="flex flex-col items-center mb-6">
        {/* Replace with your logo */}
       
        
        <h1 className="text-red-600 text-3xl font-bold">SWISS TIME HOUSE</h1>
        <span className="text-red-500 text-sm">Since 1946</span>
      </div>

      {/* Description */}
      <p className="text-center max-w-2xl mb-6 text-lg">
        Your one stop destination for all Luxury and Fashion brands with a
        glorious tradition of over 77 years.
      </p>

      {/* Social Media Icons */}
      <div className="flex gap-6">
        <a href="#" className="bg-blue-600 p-4 rounded-full hover:scale-110 transition">
          <FaFacebookF size={24} />
        </a>
        <a href="#" className="bg-gray-700 p-4 rounded-full hover:scale-110 transition">
          <FaInstagram size={24} />
        </a>
        <a href="#" className="bg-red-600 p-4 rounded-full hover:scale-110 transition">
          <FaYoutube size={24} />
        </a>
        <a href="#" className="bg-blue-500 p-4 rounded-full hover:scale-110 transition">
          <FaLinkedinIn size={24} />
        </a>
      </div>
    </div>
  );
}

export default Sto;
