import React from "react";

const NewLaunches = () => {
  const products = [
    {
      name: "Timex TWEG26300",
      oldPrice: "€133",
      price: "€107",
      discount: "-20%",
      img: "/watch2.avif",
    },
    {
      name: "Seiko SPB509J1",
      price: "€1,887",
      soldOut: true,
      img: "/watch3.avif",
    },
    {
      name: "SF-PS3-05",
      price: "€1,998",
      img: "/watch4.avif",
    },
  ];

  return (
    <div className="bg-black text-white px-10 py-16">
      {/* Header */}
      <div className="flex items-center justify-between mb-10">
        <h2 className="text-4xl font-bold">New Launches</h2>
        <button className="bg-red-600 px-6 py-2 rounded text-sm font-semibold">
          View all
        </button>
      </div>

      {/* Layout */}
      <div className="grid grid-cols-4 gap-8">
        {/* Left Promo Banner */}
        <div className="col-span-1">
          <img
            src="/NewLaunches.png"
            alt="Promo"
            className="rounded-xl w-full h-[500px] object-cover"
          />
        </div>

        {/* Product Cards */}
        <div className="col-span-3 grid grid-cols-3 gap-8">
          {products.map((product, idx) => (
            <div key={idx} className="text-center relative">
              {/* Discount Badge */}
              {product.discount && (
                <span className="absolute top-2 right-2 bg-purple-600 text-white text-xs px-2 py-1 rounded">
                  {product.discount}
                </span>
              )}

              {/* Sold Out Tag */}
              {product.soldOut && (
                <span className="absolute top-2 left-2 text-xs text-gray-300">
                  SOLD OUT
                </span>
              )}

              <img
                src={product.img}
                alt={product.name}
                className={`w-full h-72 object-contain mx-auto ${
                  idx === 0 ? "mb-10" : "mb-0"
                }`}
              />

              <p className="mt-4 font-semibold text-lg">{product.name}</p>

              {/* Old price with strikethrough */}
              {product.oldPrice && (
                <p className="text-gray-400 line-through text-sm">
                  {product.oldPrice}
                </p>
              )}

              <p className="text-lg font-bold">{product.price}</p>

              {/* Add to Cart */}
              <button className="mt-4 bg-red-600 px-6 py-2 rounded font-semibold flex items-center justify-center gap-2 w-full">
                <span>🛒</span> ADD TO CART
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NewLaunches;
