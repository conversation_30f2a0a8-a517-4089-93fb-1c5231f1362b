import React, { useEffect, useState } from "react";

const OfficialRetailer = () => {
  const [time, setTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const seconds = time.getSeconds() * 6; // 360/60
  const minutes = time.getMinutes() * 6 + seconds / 60;
  const hours = (time.getHours() % 12) * 30 + minutes / 12;

  return (
    <div className="flex items-center justify-between px-10 py-10 bg-white">
      {/* Left Side - Store Image */}
      <div className="w-2/3">
        <img
          src="/POS-banner.avif" // replace with your image
          alt="Official Retailer"
          className="rounded-xl shadow-lg"
        />
      </div>

      {/* Right Side - Info */}
      <div className="w-1/3 flex flex-col items-center text-center space-y-6">
        {/* Rolex Style Analog Clock */}
        <div className="relative w-40 h-40 border-4 border-yellow-600 rounded-full flex items-center justify-center shadow-lg">
          {/* Center Dot */}
          <div className="w-4 h-4 bg-yellow-600 rounded-full absolute z-50"></div>

          {/* Hour Hand */}
          <div
            className="absolute w-2 h-14 bg-yellow-800 rounded origin-bottom"
            style={{ transform: `rotate(${hours}deg)` }}
          ></div>

          {/* Minute Hand */}
          <div
            className="absolute w-1.5 h-20 bg-yellow-500 rounded origin-bottom"
            style={{ transform: `rotate(${minutes}deg)` }}
          ></div>

          {/* Second Hand */}
          <div
            className="absolute w-1 h-24 bg-red-600 rounded origin-bottom"
            style={{ transform: `rotate(${seconds}deg)` }}
          ></div>
        </div>

        {/* Store Info */}
        <div className="space-y-2 w-full">
          <div className="flex justify-between px-6">
            <span>Your time</span>
            <span>{time.toLocaleTimeString()}</span>
          </div>
          <div className="flex justify-between px-6">
            <span>India</span>
          </div>
          <div className="flex justify-between px-6">
            <span>Cochin</span>
            <span>{time.toLocaleTimeString()}</span>
          </div>
        </div>

        {/* Status Button */}
        <button className="bg-green-100 text-green-700 px-6 py-2 rounded-full font-medium shadow-md">
          Closed now
        </button>

        {/* Rolex Logo */}
        <div className="mt-6">
          <img
            src="/RolexLogo.png" // replace with logo
            alt="Rolex Logo"
            className="w-32 mx-auto"
          />
          <p className="text-xs text-gray-600">OFFICIAL RETAILER</p>
        </div>
      </div>
    </div>
  );
};

export default OfficialRetailer;
