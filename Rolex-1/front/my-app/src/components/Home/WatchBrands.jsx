import React from "react";

const WatchBrands = () => {
  const brands = [
    {
      name: "TISSOT",
      logo: "/tissot-logo.png", // optional, if you want to use logo instead of text
      img: "/Tissot.avif", // replace with your uploaded image
    },
    {
      name: "G-<PERSON><PERSON><PERSON>",
      img: "/gshock.avif",
    },
    {
      name: "FOSSIL",
      img: "/fossil.avif",
    },
    {
      name: "CASIO",
      img: "/casio.avif",
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 px-10 py-10 bg-white">
      {brands.map((brand, index) => (
        <div
          key={index}
          className="relative rounded-xl overflow-hidden shadow-lg group"
        >
          <img
            src={brand.img}
            alt={brand.name}
            className="w-full h-[450px] object-cover transition-transform duration-500 group-hover:scale-105"
          />

          {/* Brand Label */}
          <div className="absolute bottom-4 w-full text-center">
            <p className="text-white text-lg font-bold drop-shadow-lg">
              {brand.name}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default WatchBrands;
