import { Mail, Phone, MapPin } from "lucide-react";

export default function Footer() {
  return (
    <footer className="bg-black text-white">
      {/* Newsletter Section */}
      <div className="max-w-6xl mx-auto px-6 py-10 border-b border-gray-700">
        <div className="flex flex-col md:flex-row justify-between items-center gap-6">
          {/* Left text */}
          <div>
            <p className="uppercase text-sm">Sign up to our</p>
            <h2 className="text-4xl font-bold">newsletter</h2>
            <p className="text-gray-400 text-sm mt-2">
              Stay updated with the latest offers, trends and launches,
              subscribe to our newsletters. Get exclusive invite to private
              sales and much more.
            </p>
          </div>

          {/* Input + button */}
          <div className="flex w-full md:w-auto items-center gap-4">
            <input
              type="email"
              placeholder="Your email address"
              className="bg-transparent border-b border-gray-400 focus:outline-none text-white flex-1 md:w-80"
            />
            <button className="bg-red-600 px-5 py-2 rounded text-white font-semibold">
              Subscribe
            </button>
          </div>
        </div>
      </div>

      {/* Footer Links Section */}
      <div className="max-w-6xl mx-auto px-6 py-12 grid grid-cols-1 md:grid-cols-4 gap-8 text-sm">
        {/* Online Shopping */}
        <div>
          <h3 className="font-bold mb-4">ONLINE SHOPPING</h3>
          <ul className="space-y-2 text-gray-300">
            <li>Home</li>
            <li>Men's Watches</li>
            <li>Women's Watches</li>
            <li>Fashion Brands</li>
            <li>Luxury Brands</li>
            <li>Smart Watches</li>
            <li>New Products</li>
            <li>Price Drop</li>
          </ul>
        </div>

        {/* Customer Policies */}
        <div>
          <h3 className="font-bold mb-4">CUSTOMER POLICIES</h3>
          <ul className="space-y-2 text-gray-300">
            <li>Privacy Policy</li>
            <li>Delivery Policy</li>
            <li>Shipping Policy</li>
            <li>Return Policy</li>
            <li>Terms & Conditions</li>
          </ul>
          <h3 className="font-bold mt-6">CURRENCY</h3>
          <p className="mt-2">€ EUR &nbsp; ₹ INR &nbsp; $ USD</p>
        </div>

        {/* Useful Links */}
        <div>
          <h3 className="font-bold mb-4">USEFUL LINKS</h3>
          <ul className="space-y-2 text-gray-300">
            <li>My Account</li>
            <li>About Us</li>
            <li>Our Stores</li>
            <li>Careers</li>
            <li>Contact Us</li>
            <li>Blogs</li>
            <li>Manage Cookies</li>
          </ul>
        </div>

        {/* Corporate Address */}
        <div>
          <h3 className="font-bold mb-4">CORPORATE ADDRESS</h3>
          <p className="font-semibold">Swiss Group India Pvt Ltd</p>
          <p className="flex items-start mt-2">
            <MapPin className="w-4 h-4 mt-1 mr-2" /> Ground Floor, GCDA Complex,
            Marine Drive, Cochin - 682031
          </p>
          <p className="flex items-center mt-2">
            <Phone className="w-4 h-4 mr-2" /> +91 91420 12345
          </p>
          <p className="flex items-center mt-2 text-green-500">
            <Phone className="w-4 h-4 mr-2" /> +91 91426 44444
          </p>
          <p className="flex items-center mt-2">
            <Mail className="w-4 h-4 mr-2" /> <EMAIL>
          </p>

          {/* Social Icons */}
          <div className="flex gap-3 mt-4">
            <a className="bg-blue-600 w-8 h-8 flex items-center justify-center rounded-full">f</a>
            <a className="bg-pink-500 w-8 h-8 flex items-center justify-center rounded-full">IG</a>
            <a className="bg-red-600 w-8 h-8 flex items-center justify-center rounded-full">YT</a>
            <a className="bg-blue-400 w-8 h-8 flex items-center justify-center rounded-full">in</a>
            <a className="bg-sky-500 w-8 h-8 flex items-center justify-center rounded-full">tw</a>
          </div>
        </div>
      </div>

      {/* Bottom Popular Searches */}
      <div className="bg-white text-black px-6 py-4 text-sm">
        <p className="font-bold">POPULAR SEARCHES</p>
        <p className="mt-2 text-gray-700">
          Watches for Men | Women's Watches | SmartWatches | Wedding Gifts |
          Fashion Brands | Leather Watches | Chronograph Watches | Casio G-Shock
          | Fossil Watches | Titan Watches | Rado Watches | Tissot Watches |
          Casio Vintage | Casio Watches | Steel Watches | Sapphire Crystal
          Watches | Rolex | Omega Watches | Fastrack Watches | Watches below
          Rs.20000 | Luxury Watches | Swiss Made Watches | Gold Watches | Clocks
          | Leather Accessories | Longines Watches | Return Policy | Casio
          Edifice | Emporio Armani Watches | Kids Watches | Couple Watches |
          Seiko Watches
        </p>
      </div>
    </footer>
  );
}
