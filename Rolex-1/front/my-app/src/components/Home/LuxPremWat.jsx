import React, { useState, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import API from "../../services/api";

function LuxPremWach() {
  const [row1Watches, setRow1Watches] = useState([]);
  const [row2Watches, setRow2Watches] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await API.get("/products");
      const products = response.data;
      
      const row1 = products.filter(product => product.row === 1);
      const row2 = products.filter(product => product.row === 2);
      
      setRow1Watches(row1);
      setRow2Watches(row2);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching products:", error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white px-10 py-16">
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white px-10 py-16">
      {/* Row 1 */}
      <div className="flex items-start gap-6 mb-16">
        {/* Fixed First Card */}
        <div className="w-64 h-[420px] bg-black text-white p-6 rounded-xl flex flex-col justify-between">
          <div>
            <p className="text-sm text-yellow-400">Luxury & Premium Brands</p>
            <h2 className="text-2xl font-bold leading-tight mt-2">
              Take A Look On <br /> Our Premium <br /> Collection
            </h2>
          </div>
          <img src="/Demo1.png" alt="premium watch" className="w-full h-56 object-contain" />
          <p className="text-sm">Experience In-Store • India</p>
        </div>

        {/* Slider */}
        <Swiper
          modules={[Autoplay]}
          slidesPerView={3}
          spaceBetween={30}
          autoplay={{ delay: 4000, disableOnInteraction: false }}
          loop={true}
          className="flex-1"
        >
          {row1Watches.map((watch) => (
            <SwiperSlide key={watch._id}>
              <div className="bg-white shadow-md rounded-lg p-4 text-center">
                <img src={watch.img} alt={watch.name} className="h-48 mx-auto object-contain" />
                <h3 className="mt-3 font-semibold">{watch.name}</h3>
                <p className="text-gray-500 text-sm">{watch.model}</p>
                <p className="mt-1 font-bold">{watch.price}</p>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Row 2 */}
      <div className="flex items-start gap-6">
        <Swiper
          modules={[Autoplay]}
          slidesPerView={3}
          spaceBetween={30}
          autoplay={{ delay: 4000, disableOnInteraction: false }}
          loop={true}
          className="flex-1"
        >
          {row2Watches.map((watch) => (
            <SwiperSlide key={watch._id}>
              <div className="bg-white shadow-md rounded-lg p-4 text-center">
                <img src={watch.img} alt={watch.name} className="h-48 mx-auto object-contain" />
                <h3 className="mt-3 font-semibold">{watch.name}</h3>
                <p className="text-gray-500 text-sm">{watch.model}</p>
                <p className="mt-1 font-bold">{watch.price}</p>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Fixed Last Card */}
        <div className="w-64 h-[420px] bg-gradient-to-b from-yellow-500 to-black text-white p-6 rounded-xl flex flex-col justify-between">
          <div>
            <p className="text-sm text-yellow-300">Best Sellers</p>
            <h2 className="text-2xl font-bold leading-tight mt-2">
              Own The Very Best - <br /> Pick From <br /> Our Best Sellers
            </h2>
          </div>
          <img src="/bestwatch.png" alt="best seller watch" className="w-full h-56 object-contain" />
          <p className="text-sm">Find nearest shop • India</p>
        </div>
      </div>
    </div>
  );
}

export default LuxPremWach;
