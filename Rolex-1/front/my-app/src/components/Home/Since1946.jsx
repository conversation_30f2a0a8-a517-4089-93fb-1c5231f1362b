import React from "react";
import { Shield, Truck, RefreshCw } from "lucide-react";

const Since1946 = () => {
  return (
    <div className="bg-black text-white py-16 px-10 flex flex-col lg:flex-row justify-between items-center">
      {/* Left Side */}
      <div className="text-center lg:text-left mb-10 lg:mb-0">
        <p className="uppercase tracking-widest text-gray-400 text-sm">
          The Timekeepers
        </p>
        <h1 className="text-6xl font-bold mt-2">since 1946</h1>
      </div>

      {/* Right Side */}
      <div className="space-y-8 max-w-lg">
        {/* Authorized Retailer */}
        <div className="flex items-start space-x-4">
          <Shield className="w-8 h-8 text-white" />
          <div>
            <h3 className="text-lg font-semibold">
              Authorized Retailer - Since 1946
            </h3>
            <p className="text-gray-400 text-sm">
              Authorized retailer for over 70 brands. All products are 100% genuine and
              covered by manufacturer warranty.
            </p>
          </div>
        </div>

        {/* Free & Fast Delivery */}
        <div className="flex items-start space-x-4">
          <Truck className="w-8 h-8 text-white" />
          <div>
            <h3 className="text-lg font-semibold">Free & Fast Delivery</h3>
            <p className="text-gray-400 text-sm">
              Enjoy free shipping for all orders above Rs.1000 within India.
            </p>
          </div>
        </div>

        {/* Easy Exchange */}
        <div className="flex items-start space-x-4">
          <RefreshCw className="w-8 h-8 text-white" />
          <div>
            <h3 className="text-lg font-semibold">Easy Exchange</h3>
            <p className="text-gray-400 text-sm">
              You can now exchange the product within 7 days of purchase.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Since1946;
