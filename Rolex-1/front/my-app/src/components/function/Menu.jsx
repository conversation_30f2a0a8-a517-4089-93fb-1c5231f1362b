import React from "react";

function Manu() {
  return (
    <div className="flex flex-col h-full bg-white p-6 overflow-y-auto">
      {/* Header with Logo + Close will be controlled in Navbar */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Left Column */}
        <div className="w-full lg:w-1/4 space-y-4">
          <ul className="space-y-3 text-gray-800 font-medium">
            <li className="flex items-center justify-between hover:text-red-600 cursor-pointer">
              Watches <span>›</span>
            </li>
            <li className="flex items-center justify-between hover:text-red-600 cursor-pointer">
              Find Your Watch <span>›</span>
            </li>
            <li className="flex items-center justify-between text-red-600 cursor-pointer">
              Top Deals & Offers <span>›</span>
            </li>
            <li className="flex items-center justify-between hover:text-red-600 cursor-pointer">
              Clocks <span>›</span>
            </li>
            <li className="flex items-center justify-between hover:text-red-600 cursor-pointer">
              Accessories <span>›</span>
            </li>
            <li className="flex items-center justify-between hover:text-red-600 cursor-pointer">
              About Us <span>›</span>
            </li>
            <li className="flex items-center justify-between hover:text-red-600 cursor-pointer">
              Our Stores <span>›</span>
            </li>
            <li className="flex items-center justify-between hover:text-red-600 cursor-pointer">
              Back to Homepage <span>›</span>
            </li>
          </ul>

          {/* Logos */}
          <div className="mt-6 space-y-4">
            <img src="/Rolex-image.png" alt="Rolex" className="h-12" />
            <img src="/Tudor-image.png" alt="Tudor" className="h-12" />
            <img src="/Casio-image.png" alt="Casio" className="h-12" />
            <img src="/Longines-image.png" alt="Longines" className="h-12" />
          </div>
        </div>

        {/* Right Column */}
        <div className="w-full lg:w-3/4">
          <h2 className="text-lg font-bold mb-4">OUR BRANDS</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            {/* Example Brand Lists */}
            <div>
              <p className="font-semibold mb-2">A - E</p>
              <ul className="space-y-1">
                <li>Alba</li>
                <li>Amazfit</li>
                <li>Anne Klein</li>
                <li>Armani Exchange</li>
                <li>Balmain</li>
                <li>Beetel</li>
                <li>Boat</li>
                <li>Calvin Klein</li>
              </ul>
            </div>

            <div>
              <p className="font-semibold mb-2">E - N</p>
              <ul className="space-y-1">
                <li>Earnshaw</li>
                <li>Emporio Armani</li>
                <li>Esprit</li>
                <li>Fastrack</li>
                <li>Fossil</li>
                <li>Franck Muller</li>
                <li>GC</li>
                <li>Guess</li>
              </ul>
            </div>

            <div>
              <p className="font-semibold mb-2">O - S</p>
              <ul className="space-y-1">
                <li>Obaku</li>
                <li>Omega</li>
                <li>Police</li>
                <li>Quantum</li>
                <li>Rolex</li>
                <li>Rado</li>
                <li>Seiko</li>
                <li>Swatch</li>
              </ul>
            </div>

            <div>
              <p className="font-semibold mb-2">T - Z</p>
              <ul className="space-y-1">
                <li>Tag Heuer</li>
                <li>Timex</li>
                <li>Tissot</li>
                <li>Titan</li>
                <li>Tommy Hilfiger</li>
                <li>Tudor</li>
                <li>Victorinox</li>
                <li>Zoop</li>
              </ul>
            </div>
          </div>

          {/* Promo Banner */}
          <div className="mt-6">
            <img
              src="/Tissot-promo.png"
              alt="Tissot Promo"
              className="rounded shadow-md"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Manu;
