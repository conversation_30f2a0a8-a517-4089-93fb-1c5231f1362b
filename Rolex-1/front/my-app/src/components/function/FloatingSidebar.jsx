import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaSearch, FaShoppingCart, FaComments } from "react-icons/fa";

function FloatingSidebar() {
  return (
    <div className="fixed top-1/4 right-0 flex flex-col items-center bg-white shadow-lg rounded-l-xl p-3 space-y-6 z-50">
      {/* Home */}
      <button className="text-xl hover:text-blue-500">
        <FaHome />
      </button>

      {/* Menu */}
      <button className="text-xl hover:text-blue-500">
        <FaBars />
      </button>

      {/* Profile */}
      <button className="text-xl hover:text-blue-500">
        <FaUser />
      </button>

      {/* Search */}
      <button className="text-xl hover:text-blue-500">
        <FaSearch />
      </button>

      {/* Cart with badge */}
      <div className="relative">
        <button className="text-xl hover:text-blue-500">
          <FaShoppingCart />
        </button>
        <span className="absolute -top-2 -right-2 bg-red-600 text-white text-xs px-1.5 py-0.5 rounded-full">
          1
        </span>
      </div>

      {/* Chat */}
      <button className="text-xl hover:text-blue-500">
        <FaComments />
      </button>
    </div>
  );
}

export default FloatingSidebar;
