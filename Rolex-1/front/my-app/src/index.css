/* Add Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
/* Add Clash Display font */
@import url('https://api.fontshare.com/v2/css?f[]=clash-display@400,600,700&display=swap');
/* Add Montserrat for headings */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
    .stroke-white {
      -webkit-text-stroke: 1px white;
    }
  }
  

@keyframes marquee {
    0%   { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
  }
@layer base {
  :root {
    --background: 250 30% 98%;
    --foreground: 250 20% 10%;

    --card: 0 0% 100%;
    --card-foreground: 250 20% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 250 20% 10%;

    --primary: 260 80% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 270 70% 60%;
    --secondary-foreground: 0 0% 100%;

    --muted: 250 30% 96%;
    --muted-foreground: 250 10% 40%;

    --accent: 280 90% 70%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 250 30% 90%;
    --input: 250 30% 90%;
    --ring: 260 80% 50%;

    --radius: 1rem;

    /* New colors */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 0%;
    
    --info: 198 93% 60%;
    --info-foreground: 0 0% 100%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(260 80% 50%), hsl(280 70% 45%));
    --gradient-purple: linear-gradient(135deg, #9333EA, #7E22CE);
    --gradient-lavender: linear-gradient(135deg, #A78BFA, #8B5CF6);
    --gradient-teal: linear-gradient(135deg, #14B8A6, #0F766E);
  }

  .dark {
    --background: 250 20% 5%;
    --foreground: 250 10% 95%;

    --card: 250 20% 8%;
    --card-foreground: 250 10% 95%;

    --popover: 250 20% 8%;
    --popover-foreground: 250 10% 95%;

    --primary: 260 70% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 270 60% 40%;
    --secondary-foreground: 0 0% 100%;

    --muted: 250 20% 15%;
    --muted-foreground: 250 10% 70%;

    --accent: 280 70% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%;

    --border: 250 20% 20%;
    --input: 250 20% 20%;
    --ring: 260 70% 60%;
    
    /* New colors for dark mode */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    
    --warning: 48 96% 53%;
    --warning-foreground: 0 0% 0%;
    
    --info: 198 93% 60%;
    --info-foreground: 0 0% 100%;
    
    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(260 70% 60%), hsl(280 60% 45%));
    --gradient-purple: linear-gradient(135deg, #A855F7, #7E22CE);
    --gradient-lavender: linear-gradient(135deg, #C4B5FD, #A78BFA);
    --gradient-teal: linear-gradient(135deg, #2DD4BF, #0F766E);
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', 'Clash Display', sans-serif;
    @apply tracking-tight;
  }
}

@layer components {
  /* Buttons */
  .btn-purple {
    @apply bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 transition-all duration-300;
  }
  
  .btn-purple-outline {
    @apply border-2 border-purple-300 dark:border-purple-800 text-purple-700 dark:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/30 transition-all duration-300;
  }
  
  /* Cards */
  .card-purple {
    @apply border-purple-100 dark:border-purple-900/30 shadow-sm hover:shadow-md transition-all duration-300;
  }
  
  /* Gradients */
  .bg-purple-gradient {
    @apply bg-gradient-to-br from-purple-800 via-violet-700 to-indigo-900;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent;
  }
  
  /* Icons */
  .icon-purple {
    @apply bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center shadow-md text-white;
  }
  
  /* Hero sections */
  .hero-section {
    @apply relative overflow-hidden rounded-3xl bg-gradient-to-br from-purple-800 via-violet-700 to-indigo-900 p-8 md:p-12;
  }
  
  .hero-glow {
    @apply absolute top-0 left-0 w-full h-full opacity-10;
  }
  
  .hero-glow::before {
    content: '';
    @apply absolute top-10 left-10 w-64 h-64 rounded-full bg-white/20 blur-3xl;
  }
  
  .hero-glow::after {
    content: '';
    @apply absolute bottom-10 right-10 w-80 h-80 rounded-full bg-purple-300/20 blur-3xl;
  }
  
  /* Animations */
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes pulse-glow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }
  
  .animate-pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
  }
  
  /* Misc utilities */
  .shadow-soft {
    @apply shadow-[0_4px_20px_-2px_rgba(0,0,0,0.05)];
  }
  
  .shadow-purple {
    @apply shadow-[0_0_15px_rgba(124,58,237,0.3)];
  }
}