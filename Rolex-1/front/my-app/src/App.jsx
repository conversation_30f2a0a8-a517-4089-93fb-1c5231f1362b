import React from "react";
import { Routes, Route } from "react-router-dom";
import Home from "./pages/Home";
import OfficialRetailers from "./components/function/OfficialRetailers";
import Login from "./pages/Login";
import AdminPanel from "./pages/AdminPanel";
import ProtectedRoute from "./components/function/ProtectedRoute";

export default function App() {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/official-retailers" element={<OfficialRetailers />} />

      <Route path="/login" element={<Login />} />
      <Route
        path="/admin"
        element={
          <ProtectedRoute>
            <AdminPanel />
          </ProtectedRoute>
        }
      />
    </Routes>
  );
}
