import React, { useEffect, useState } from "react";
import API from "../services/api";

export default function AdminPanel() {
  const [products, setProducts] = useState([]);
  const [form, setForm] = useState({ name: "", model: "", price: "", img: "", row: 1 });
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [imagePreview, setImagePreview] = useState("");

  const load = async () => {
    setLoading(true);
    const { data } = await API.get("/products");
    setProducts(data);
    setLoading(false);
  };

  useEffect(() => { load(); }, []);

  const onChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  // Handle image upload
  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setUploading(true);
    const formData = new FormData();
    formData.append("image", file);

    try {
      const response = await API.post("/upload", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      
      const imageUrl = response.data.url;
      setForm({ ...form, img: imageUrl });
      setImagePreview(imageUrl);
    } catch (error) {
      console.error("Error uploading image:", error);
      alert("Error uploading image. Please try again.");
    } finally {
      setUploading(false);
    }
  };

  const add = async (e) => {
    e.preventDefault();
    if (!form.img) {
      alert("Please upload an image first");
      return;
    }
    
    await API.post("/products", { ...form, row: Number(form.row) });
    setForm({ name: "", model: "", price: "", img: "", row: 1 });
    setImagePreview("");
    load();
  };

  const remove = async (id) => {
    if (window.confirm("Are you sure you want to delete this product?")) {
      await API.delete(`/products/${id}`);
      load();
    }
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Admin Panel - Product Management</h1>
      
      <form onSubmit={add} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8 p-6 bg-gray-50 rounded-lg shadow">
        <input 
          className="border p-3 rounded" 
          name="name" 
          placeholder="Brand Name" 
          value={form.name} 
          onChange={onChange} 
          required 
        />
        <input 
          className="border p-3 rounded" 
          name="model" 
          placeholder="Model" 
          value={form.model} 
          onChange={onChange} 
          required 
        />
        <input 
          className="border p-3 rounded" 
          name="price" 
          placeholder="Price (e.g. € 2,020)" 
          value={form.price} 
          onChange={onChange} 
          required 
        />
        
        <div className="col-span-1">
          <label className="block text-sm font-medium text-gray-700 mb-2">Product Image</label>
          <input 
            type="file" 
            accept="image/*" 
            onChange={handleImageUpload} 
            className="border p-3 rounded w-full"
          />
          {uploading && <p className="text-sm text-blue-600 mt-1">Uploading...</p>}
          {imagePreview && (
            <img 
              src={imagePreview} 
              alt="Preview" 
              className="mt-2 w-20 h-20 object-cover rounded"
            />
          )}
        </div>
        
        <select 
          className="border p-3 rounded" 
          name="row" 
          value={form.row} 
          onChange={onChange}
        >
          <option value={1}>Row 1 (Left Slider)</option>
          <option value={2}>Row 2 (Right Slider)</option>
        </select>
        
        <button 
          type="submit"
          className="bg-blue-600 text-white rounded px-4 py-3 hover:bg-blue-700 transition duration-200"
        >
          Add Product
        </button>
      </form>

      <div className="bg-white rounded-lg shadow">
        <h2 className="text-xl font-semibold p-4 border-b">Current Products</h2>
        {loading ? (
          <div className="flex justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4">Image</th>
                  <th className="text-left p-4">Brand</th>
                  <th className="text-left p-4">Model</th>
                  <th className="text-left p-4">Price</th>
                  <th className="text-left p-4">Row</th>
                  <th className="text-left p-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {products.map((p) => (
                  <tr key={p._id} className="border-b hover:bg-gray-50">
                    <td className="p-4">
                      <img 
                        src={p.img} 
                        alt={p.name} 
                        className="w-16 h-16 object-cover rounded"
                      />
                    </td>
                    <td className="p-4 font-semibold">{p.name}</td>
                    <td className="p-4">{p.model}</td>
                    <td className="p-4">{p.price}</td>
                    <td className="p-4">Row {p.row}</td>
                    <td className="p-4">
                      <button 
                        onClick={() => remove(p._id)} 
                        className="text-red-600 hover:text-red-800 font-medium"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {products.length === 0 && (
              <p className="text-center p-8 text-gray-500">No products found</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
