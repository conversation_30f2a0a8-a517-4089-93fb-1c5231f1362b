import React from 'react'
import Navbar from '../components/Home/Navbar'
import Name from '../components/Home/Name'
import SkyDeveler from '../components/Home/SkyDeveler'
import FeatureBrand from '../components/Home/FeaturedBrand'
import Sto from '../components/Home/Sto';
import DiscoverCollection from "../components/Home/DiscoverCollection";
import StoreLocations from "../components/Home/StoreLocations";
import LuxPremWach from "../components/Home/LuxPremWat";
import OfficialRetailer from "../components/Home/OfficialRetailer";
import WatchBrands from "../components/Home/WatchBrands";
import Since1946 from "../components/Home/Since1946";
import WatchOffers from "../components/Home/WatchOffers";
import NewLaunches from "../components/Home/NewLaunches";
import RolexArt from "../components/Home/RolexArt";
import Carousel from "../components/Home/Carousel";
import Footer from "../components/Home/Footer";

function Home(){
    return(
        <>
      <Navbar />
      <div className="pt-10">  {/* Push Name down by Navbar height */}
        <Name />
        <SkyDeveler/>
        <FeatureBrand/>
        <Sto/>
        <DiscoverCollection/>
        <StoreLocations/>
        <LuxPremWach/>
        <OfficialRetailer/>
        <WatchBrands/>
        <Since1946/>
        <WatchOffers/>
        <NewLaunches/>
        <RolexArt/>
        <Carousel/>
        <Footer/>
       
        
        
      </div>
    </>
    )
    
}
export default Home