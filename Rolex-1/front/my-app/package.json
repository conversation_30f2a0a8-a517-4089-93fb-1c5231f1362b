{"name": "my-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.11.0", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.1", "swiper": "^11.2.10"}, "devDependencies": {"@eslint/js": "^9.33.0", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "vite": "^7.1.2"}}